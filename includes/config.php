<?php
// Application configuration

// Directory settings
define('LISTS_DIR', 'url_lists');

// Validation limits
define('MAX_LIST_NAME_LENGTH', 50);
define('MAX_URLS_PER_LIST', 100);

// File settings
define('FILE_EXTENSION', '.json');
define('FILE_PERMISSIONS', 0755);

// Error reporting (disable in production)
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Create lists directory if it doesn't exist
if (!is_dir(LISTS_DIR)) {
    if (!mkdir(LISTS_DIR, FILE_PERMISSIONS, true)) {
        die('Failed to create lists directory');
    }
}

// Security headers
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: DENY');
header('X-XSS-Protection: 1; mode=block');
?>
