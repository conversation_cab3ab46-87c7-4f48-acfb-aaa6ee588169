<?php
/**
 * Core functions for URL list management
 */

/**
 * Save a URL list to file
 */
function saveList($listName, $urls) {
    $filename = sanitizeFilename($listName);
    $filepath = LISTS_DIR . '/' . $filename . FILE_EXTENSION;

    $data = [
        'name' => $listName,
        'urls' => $urls,
        'created' => date('Y-m-d H:i:s'),
        'count' => count($urls)
    ];

    $jsonData = json_encode($data, JSON_PRETTY_PRINT);
    return file_put_contents($filepath, $jsonData) !== false;
}

/**
 * Load a URL list from file
 */
function loadList($listName) {
    $filename = sanitizeFilename($listName);
    $filepath = LISTS_DIR . '/' . $filename . FILE_EXTENSION;

    if (!file_exists($filepath)) {
        return false;
    }

    $data = json_decode(file_get_contents($filepath), true);
    return $data ?: false;
}

/**
 * Delete a URL list file
 */
function deleteList($listName) {
    $filename = sanitizeFilename($listName);
    $filepath = LISTS_DIR . '/' . $filename . FILE_EXTENSION;

    if (file_exists($filepath)) {
        return unlink($filepath);
    }

    return false;
}

/**
 * Get all saved lists
 */
function getAllLists() {
    $lists = [];

    if (!is_dir(LISTS_DIR)) {
        return $lists;
    }

    $files = glob(LISTS_DIR . '/*' . FILE_EXTENSION);

    foreach ($files as $file) {
        $data = json_decode(file_get_contents($file), true);
        if ($data && isset($data['name'])) {
            $lists[] = [
                'name' => $data['name'],
                'count' => $data['count'] ?? 0,
                'created' => $data['created'] ?? ''
            ];
        }
    }

    // Sort by creation date, newest first
    usort($lists, function($a, $b) {
        return strtotime($b['created']) - strtotime($a['created']);
    });

    return $lists;
}

/**
 * Sanitize filename for safe file operations
 */
function sanitizeFilename($filename) {
    // Remove or replace dangerous characters
    $filename = preg_replace('/[^a-zA-Z0-9_-]/', '_', $filename);

    // Trim and limit length
    $filename = substr(trim($filename), 0, MAX_LIST_NAME_LENGTH);

    // Ensure we have something to work with
    if (empty($filename)) {
        $filename = 'unnamed_' . time();
    }

    return $filename;
}

/**
 * Validate URL format
 */
function isValidUrl($url) {
    return filter_var($url, FILTER_VALIDATE_URL) &&
           (strpos($url, 'http://') === 0 || strpos($url, 'https://') === 0);
}

/**
 * Log errors safely
 */
function logError($message) {
    error_log("[URL Manager] " . $message);
}
?>
