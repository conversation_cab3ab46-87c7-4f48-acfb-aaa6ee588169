/**
 * Predefined URL collections for quick access
 */

const presets = {
    social: [
        'facebook.com',
        'twitter.com',
        'instagram.com',
        'linkedin.com',
        'reddit.com'
    ],
    news: [
        'bbc.com/news',
        'cnn.com',
        'reuters.com',
        'npr.org',
        'theguardian.com'
    ],
    dev: [
        'github.com',
        'stackoverflow.com',
        'developer.mozilla.org',
        'codepen.io',
        'w3schools.com'
    ],
    productivity: [
        'gmail.com',
        'calendar.google.com',
        'drive.google.com',
        'trello.com',
        'slack.com'
    ]
};

/**
 * Load a preset URL collection
 * @param {string} type - Preset type (social, news, dev, productivity)
 */
function loadPreset(type) {
    if (presets[type]) {
        document.getElementById('urlList').value = presets[type].join('\n');
        showStatus(`Loaded ${presets[type].length} ${type} URLs`, 'success');
    }
}
