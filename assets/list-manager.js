/**
 * List management functions for saving/loading URL collections
 */

/**
 * Save the current URL list with a custom name
 */
async function saveCurrentList() {
    const urlList = document.getElementById('urlList').value;
    const listName = document.getElementById('newListName').value.trim();

    if (!urlList.trim()) {
        showStatus('No URLs to save!', 'error');
        return;
    }

    if (!listName) {
        showStatus('Please enter a list name!', 'error');
        return;
    }

    const urls = parseUrls(urlList);

    if (urls.length === 0) {
        showStatus('No valid URLs to save!', 'error');
        return;
    }

    try {
        const result = await makeRequest({
            action: 'save_list',
            name: listName,
            urls: urls
        });

        if (result.success) {
            showStatus(`✅ Saved "${listName}" (${urls.length} URLs)`, 'success');
            document.getElementById('newListName').value = '';
            await refreshSavedLists();
        } else {
            showStatus(`❌ ${result.message}`, 'error');
        }
    } catch (error) {
        showStatus('❌ Failed to save list', 'error');
        console.error('Save error:', error);
    }
}

/**
 * Load a saved URL list
 * @param {string} listName - Name of the list to load
 */
async function loadSavedList(listName) {
    try {
        const result = await makeRequest({
            action: 'load_list',
            name: listName
        });

        if (result.success) {
            document.getElementById('urlList').value = result.data.urls.join('\n');
            showStatus(`✅ Loaded "${listName}" (${result.data.count} URLs)`, 'success');
        } else {
            showStatus(`❌ ${result.message}`, 'error');
        }
    } catch (error) {
        showStatus('❌ Failed to load list', 'error');
        console.error('Load error:', error);
    }
}

/**
 * Delete a saved URL list
 * @param {string} listName - Name of the list to delete
 */
async function deleteSavedList(listName) {
    if (!confirm(`Delete list "${listName}"?`)) {
        return;
    }

    try {
        const result = await makeRequest({
            action: 'delete_list',
            name: listName
        });

        if (result.success) {
            showStatus(`🗑️ Deleted "${listName}"`, 'success');
            await refreshSavedLists();
        } else {
            showStatus(`❌ ${result.message}`, 'error');
        }
    } catch (error) {
        showStatus('❌ Failed to delete list', 'error');
        console.error('Delete error:', error);
    }
}

/**
 * Refresh the saved lists display
 */
async function refreshSavedLists() {
    try {
        const result = await makeRequest({
            action: 'get_all_lists'
        });

        if (result.success) {
            updateSavedListsDisplay(result.lists);
        }
    } catch (error) {
        console.error('Refresh error:', error);
    }
}

/**
 * Update the saved lists HTML display
 * @param {Array} lists - Array of list objects
 */
function updateSavedListsDisplay(lists) {
    const container = document.getElementById('savedListsContainer');

    if (lists.length === 0) {
        container.innerHTML = '<p style="text-align: center; color: #666; font-style: italic;">No saved lists yet</p>';
        return;
    }

    container.innerHTML = lists.map(list => `
        <div class="saved-list-item">
            <div class="saved-list-info">
                <div class="saved-list-name">${escapeHtml(list.name)}</div>
                <div class="saved-list-meta">
                    ${list.count} URLs • ${formatDate(list.created)}
                </div>
            </div>
            <div class="saved-list-actions">
                <button class="load-btn" onclick="loadSavedList('${escapeHtml(list.name)}')">
                    📁 Load
                </button>
                <button class="delete-btn" onclick="deleteSavedList('${escapeHtml(list.name)}')">
                    🗑️ Delete
                </button>
            </div>
        </div>
    `).join('');
}
