/**
 * UI management and interaction functions
 */

/**
 * Show status message to user
 * @param {string} message - Message to display
 * @param {string} type - Message type ('success' or 'error')
 */
function showStatus(message, type) {
    const statusDiv = document.getElementById('status');
    statusDiv.textContent = message;
    statusDiv.className = `status ${type}`;
    statusDiv.style.display = 'block';
    
    // Auto-hide success messages after 3 seconds
    if (type === 'success') {
        setTimeout(() => {
            statusDiv.style.display = 'none';
        }, 3000);
    }
}

/**
 * Setup keyboard shortcuts
 */
function setupKeyboardShortcuts() {
    // Ctrl+Enter to open tabs from URL textarea
    document.getElementById('urlList').addEventListener('keydown', function(e) {
        if (e.ctrlKey && e.key === 'Enter') {
            e.preventDefault();
            openAllTabs();
        }
    });
    
    // Enter to save list from name input
    document.getElementById('newListName').addEventListener('keydown', function(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            saveCurrentList();
        }
    });
}

/**
 * Setup form validation
 */
function setupFormValidation() {
    const listNameInput = document.getElementById('newListName');
    
    // Real-time validation for list name
    listNameInput.addEventListener('input', function() {
        const value = this.value.trim();
        const saveBtn = document.querySelector('.save-btn');
        
        if (value.length === 0) {
            this.style.borderColor = '#dee2e6';
            saveBtn.disabled = false;
        } else if (value.length > 50) {
            this.style.borderColor = '#dc3545';
            saveBtn.disabled = true;
        } else {
            this.style.borderColor = '#28a745';
            saveBtn.disabled = false;
        }
    });
}

/**
 * Setup URL textarea enhancements
 */
function setupUrlTextarea() {
    const textarea = document.getElementById('urlList');
    
    // Add line numbers or URL count display
    textarea.addEventListener('input', function() {
        const rawUrls = this.value.split('\n')
            .map(url => url.trim())
            .filter(url => url.length > 0);
            
        const processedUrls = rawUrls.map(url => autoAddProtocol(url));
        const validUrls = processedUrls.filter(url => isValidUrl(url));
        const invalidUrls = rawUrls.filter((url, index) => 
            url.length > 0 && !isValidUrl(processedUrls[index]) && !isDomainLike(url)
        );
        
        // Update helper text
        if (rawUrls.length > 0) {
            let helperText = `${validUrls.length} valid URLs`;
            if (invalidUrls.length > 0) {
                helperText += `, ${invalidUrls.length} invalid`;
            }
            
            const autoFixedCount = processedUrls.filter((url, index) => 
                url !== rawUrls[index] && isValidUrl(url)
            ).length;
            
            if (autoFixedCount > 0) {
                helperText += ` (${autoFixedCount} auto-fixed)`;
            }
            
            updateUrlCount(helperText);
        } else {
            updateUrlCount('');
        }
    });
    
    // Auto-fix URLs on blur (when user clicks away)
    textarea.addEventListener('blur', function() {
        const lines = this.value.split('\n');
        const processedLines = lines.map(line => {
            const trimmed = line.trim();
            if (trimmed.length === 0) return line;
            
            const processed = autoAddProtocol(trimmed);
            // Only replace if we actually fixed something
            return processed !== trimmed ? processed : line;
        });
        
        const newValue = processedLines.join('\n');
        if (newValue !== this.value) {
            this.value = newValue;
            showStatus('✨ Auto-added protocols to domain names', 'success');
        }
    });
}

/**
 * Update URL count display
 * @param {string} text - Count text to display
 */
function updateUrlCount(text) {
    let countDiv = document.getElementById('urlCount');
    if (!countDiv) {
        countDiv = document.createElement('div');
        countDiv.id = 'urlCount';
        countDiv.style.cssText = `
            font-size: 12px;
            color: #666;
            margin-top: 5px;
            text-align: right;
        `;
        document.getElementById('urlList').parentNode.appendChild(countDiv);
    }
    countDiv.textContent = text;
}

/**
 * Setup tooltips for buttons
 */
function setupTooltips() {
    const tooltips = {
        '.open-btn': 'Open all URLs in new tabs (Ctrl+Enter)',
        '.clear-btn': 'Clear all URLs from the input',
        '.save-btn': 'Save current URLs as a named list',
        '.load-btn': 'Load this list into the input area',
        '.delete-btn': 'Permanently delete this saved list'
    };
    
    Object.entries(tooltips).forEach(([selector, tooltip]) => {
        const elements = document.querySelectorAll(selector);
        elements.forEach(element => {
            element.title = tooltip;
        });
    });
}

/**
 * Handle responsive layout adjustments
 */
function handleResponsiveLayout() {
    const mediaQuery = window.matchMedia('(max-width: 768px)');
    
    function handleScreenChange(e) {
        const saveSection = document.querySelector('.save-new-section');
        const input = saveSection.querySelector('input');
        const button = saveSection.querySelector('button');
        
        if (e.matches) {
            // Mobile layout
            input.style.width = '100%';
            input.style.marginBottom = '10px';
            input.style.marginRight = '0';
            button.style.width = '100%';
        } else {
            // Desktop layout
            input.style.width = '200px';
            input.style.marginBottom = '0';
            input.style.marginRight = '10px';
            button.style.width = 'auto';
        }
    }
    
    mediaQuery.addListener(handleScreenChange);
    handleScreenChange(mediaQuery);
}
