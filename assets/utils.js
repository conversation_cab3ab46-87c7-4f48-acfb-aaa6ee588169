/**
 * Utility functions for the URL manager
 */

/**
 * Make an AJAX request to the API
 * @param {Object} data - Request data
 * @returns {Promise} Response promise
 */
async function makeRequest(data) {
    try {
        const response = await fetch('api.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        return await response.json();
    } catch (error) {
        console.error('Request failed:', error);
        throw error;
    }
}

/**
 * Validate URL format
 * @param {string} url - URL to validate
 * @returns {boolean} True if valid
 */
function isValidUrl(url) {
    try {
        new URL(url);
        return url.startsWith('http://') || url.startsWith('https://');
    } catch (_) {
        return false;
    }
}

/**
 * Escape HTML to prevent XSS
 * @param {string} text - Text to escape
 * @returns {string} Escaped text
 */
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

/**
 * Parse URLs from textarea content and auto-add protocols
 * @param {string} content - Textarea content
 * @returns {Array} Array of processed URLs
 */
function parseUrls(content) {
    return content.split('\n')
        .map(url => url.trim())
        .filter(url => url.length > 0)
        .map(url => autoAddProtocol(url));
}

/**
 * Auto-add protocol to URLs that are missing it
 * @param {string} url - URL to process
 * @returns {string} URL with protocol
 */
function autoAddProtocol(url) {
    // Skip if already has protocol
    if (url.startsWith('http://') || url.startsWith('https://')) {
        return url;
    }
    
    // Skip if it's not a domain-like string
    if (!isDomainLike(url)) {
        return url;
    }
    
    // Add https by default (more secure)
    return 'https://' + url;
}

/**
 * Check if a string looks like a domain name
 * @param {string} str - String to check
 * @returns {boolean} True if it looks like a domain
 */
function isDomainLike(str) {
    // Basic domain pattern: contains at least one dot and valid characters
    const domainPattern = /^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;
    
    // Must contain at least one dot
    if (!str.includes('.')) {
        return false;
    }
    
    // Must not contain spaces or invalid characters
    if (/\s/.test(str)) {
        return false;
    }
    
    // Check against domain pattern
    return domainPattern.test(str);
}

/**
 * Clear the URL input textarea
 */
function clearUrls() {
    if (confirm('Clear all URLs?')) {
        document.getElementById('urlList').value = '';
        document.getElementById('status').style.display = 'none';
    }
}

/**
 * Format date for display
 * @param {string} dateString - ISO date string
 * @returns {string} Formatted date
 */
function formatDate(dateString) {
    return new Date(dateString).toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric'
    });
}
