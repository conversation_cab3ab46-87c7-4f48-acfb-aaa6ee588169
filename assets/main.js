/**
 * Main application initialization and event handling
 */

/**
 * Initialize the application when DOM is loaded
 */
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

/**
 * Main application initialization
 */
function initializeApp() {
    console.log('Multi-Tab Opener Pro - Initializing...');

    // Setup all UI enhancements
    setupKeyboardShortcuts();
    setupFormValidation();
    setupUrlTextarea();
    setupTooltips();
    handleResponsiveLayout();

    // Test popup blocker on load (but don't show error immediately)
    testPopupCapability();

    console.log('Multi-Tab Opener Pro - Ready!');
}

/**
 * Test popup capability without showing immediate error
 */
function testPopupCapability() {
    // Delayed test to avoid false positives
    setTimeout(() => {
        const testPopup = window.open('', '_blank', 'width=1,height=1');
        if (testPopup) {
            testPopup.close();
            console.log('Popups are allowed');
        } else {
            console.warn('Popup blocker detected - will show warning when user tries to open tabs');
        }
    }, 1000);
}

/**
 * Handle application errors gracefully
 */
window.addEventListener('error', function(event) {
    console.error('Application error:', event.error);
    showStatus('An unexpected error occurred. Please try again.', 'error');
});

/**
 * Handle unhandled promise rejections
 */
window.addEventListener('unhandledrejection', function(event) {
    console.error('Unhandled promise rejection:', event.reason);
    showStatus('A network error occurred. Please check your connection.', 'error');
    event.preventDefault();
});

/**
 * Prevent form submission (if any forms are accidentally created)
 */
document.addEventListener('submit', function(event) {
    event.preventDefault();
    console.log('Form submission prevented');
});

/**
 * Handle page visibility changes (pause/resume functionality)
 */
document.addEventListener('visibilitychange', function() {
    if (document.hidden) {
        console.log('Page hidden');
    } else {
        console.log('Page visible');
        // Could refresh saved lists here if needed
    }
});

/**
 * Cleanup function (called when page unloads)
 */
window.addEventListener('beforeunload', function() {
    console.log('Multi-Tab Opener Pro - Cleaning up...');
    // Any cleanup code would go here
});

/**
 * Export functions for global access (if needed)
 */
window.TabOpener = {
    openAllTabs,
    loadPreset,
    saveCurrentList,
    loadSavedList,
    deleteSavedList,
    clearUrls,
    showStatus
};
