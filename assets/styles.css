/* Multi-Tab Opener Pro - Main Styles */

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
}

h1 {
    text-align: center;
    color: #4a5568;
    margin-bottom: 30px;
    font-size: 2.5em;
    background: linear-gradient(45deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.main-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-bottom: 30px;
}

.url-input-section {
    padding: 20px;
    background: #f8f9fa;
    border-radius: 10px;
    border: 2px solid #e9ecef;
}

.saved-lists-section {
    padding: 20px;
    background: #f0f8ff;
    border-radius: 10px;
    border: 2px solid #e3f2fd;
}

textarea {
    width: 100%;
    height: 250px;
    padding: 15px;
    border: 2px solid #dee2e6;
    border-radius: 8px;
    font-family: monospace;
    font-size: 14px;
    resize: vertical;
    box-sizing: border-box;
    transition: border-color 0.3s ease;
}

textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

input[type="text"] {
    padding: 10px;
    border: 2px solid #dee2e6;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

input[type="text"]:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.button-group {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    justify-content: center;
    margin: 20px 0;
}

button {
    padding: 10px 20px;
    font-size: 14px;
    font-weight: 600;
    border: none;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 120px;
}

.open-btn {
    background: linear-gradient(45deg, #28a745, #20c997);
    color: white;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
    font-size: 16px;
    padding: 12px 30px;
}

.open-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
}

.save-btn {
    background: linear-gradient(45deg, #007bff, #6610f2);
    color: white;
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
}

.save-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
}

.load-btn {
    background: linear-gradient(45deg, #17a2b8, #6f42c1);
    color: white;
    box-shadow: 0 4px 15px rgba(23, 162, 184, 0.3);
    min-width: 80px;
    padding: 8px 15px;
    font-size: 12px;
}

.load-btn:hover {
    transform: translateY(-2px);
}

.delete-btn {
    background: linear-gradient(45deg, #dc3545, #fd7e14);
    color: white;
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
    min-width: 80px;
    padding: 8px 15px;
    font-size: 12px;
}

.delete-btn:hover {
    transform: translateY(-2px);
}

.preset-btn {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.preset-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.clear-btn {
    background: linear-gradient(45deg, #6c757d, #495057);
    color: white;
    box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
}

.clear-btn:hover {
    transform: translateY(-2px);
}

.info {
    background: #e3f2fd;
    border-left: 4px solid #2196f3;
    padding: 15px;
    margin: 20px 0;
    border-radius: 5px;
    font-size: 14px;
}

.warning {
    background: #fff3cd;
    border-left: 4px solid #ffc107;
    padding: 15px;
    margin: 20px 0;
    border-radius: 5px;
    font-size: 14px;
}

.status {
    text-align: center;
    margin: 20px 0;
    padding: 15px;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.status.success {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
}

.status.error {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

.presets {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 15px;
    margin: 20px 0;
}

.saved-list-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    margin: 8px 0;
    background: white;
    border-radius: 8px;
    border-left: 4px solid #667eea;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.saved-list-info {
    flex-grow: 1;
}

.saved-list-name {
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
}

.saved-list-meta {
    font-size: 12px;
    color: #666;
}

.saved-list-actions {
    display: flex;
    gap: 5px;
}

.save-new-section {
    margin-top: 20px;
    padding: 15px;
    background: #f0f8ff;
    border-radius: 8px;
    border: 1px dashed #2196f3;
}

.save-new-section input {
    width: 200px;
    margin-right: 10px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .main-content {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    body {
        padding: 10px;
    }

    .container {
        padding: 20px;
    }

    h1 {
        font-size: 2em;
    }

    .button-group {
        flex-direction: column;
        align-items: center;
    }

    button {
        width: 100%;
        max-width: 300px;
    }

    .saved-list-item {
        flex-direction: column;
        gap: 10px;
    }

    .saved-list-actions {
        width: 100%;
        justify-content: center;
    }

    .save-new-section input {
        width: 100%;
        margin-bottom: 10px;
        margin-right: 0;
    }
}
