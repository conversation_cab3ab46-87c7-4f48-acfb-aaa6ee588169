/**
 * Tab opening and popup management functions
 */

/**
 * Advanced tab opening with better popup handling
 * @param {Array} urls - Array of URLs to open
 * @param {Object} options - Opening options
 */
function openTabsAdvanced(urls, options = {}) {
    const {
        delay = 100,
        batchSize = 5,
        showProgress = true
    } = options;

    let openedCount = 0;
    let failedCount = 0;
    let currentBatch = 0;

    function openBatch(startIndex) {
        const endIndex = Math.min(startIndex + batchSize, urls.length);
        const batch = urls.slice(startIndex, endIndex);

        batch.forEach((url, batchIndex) => {
            const globalIndex = startIndex + batchIndex;

            setTimeout(() => {
                try {
                    const newTab = window.open(url, '_blank', 'noopener,noreferrer');
                    if (newTab) {
                        openedCount++;
                        if (showProgress) {
                            console.log(`Opened tab ${globalIndex + 1}/${urls.length}: ${url}`);
                        }
                    } else {
                        failedCount++;
                        console.warn(`Failed to open tab ${globalIndex + 1}: ${url}`);
                    }

                    // Check if this is the last URL in the batch
                    if (batchIndex === batch.length - 1) {
                        // If there are more URLs, open the next batch
                        if (endIndex < urls.length) {
                            setTimeout(() => openBatch(endIndex), delay * 2);
                        } else {
                            // All done, show final status
                            setTimeout(() => {
                                if (failedCount === 0) {
                                    showStatus(`🚀 Successfully opened ${openedCount} tabs!`, 'success');
                                } else {
                                    showStatus(`⚠️ Opened ${openedCount} tabs, ${failedCount} failed`, 'error');
                                }
                            }, 200);
                        }
                    }
                } catch (error) {
                    failedCount++;
                    console.error(`Error opening tab ${globalIndex + 1}:`, error);
                }
            }, batchIndex * delay);
        });
    }

    // Start opening the first batch
    openBatch(0);
}

/**
 * Check browser popup settings and capabilities
 */
function checkPopupCapabilities() {
    return new Promise((resolve) => {
        // Test if popups are allowed
        const testPopup = window.open('', '_blank', 'width=1,height=1');

        if (testPopup) {
            testPopup.close();
            resolve({
                allowed: true,
                message: 'Popups are enabled'
            });
        } else {
            resolve({
                allowed: false,
                message: 'Popup blocker detected'
            });
        }
    });
}

/**
 * Smart tab opening with fallback strategies
 * @param {Array} urls - URLs to open
 */
async function smartTabOpening(urls) {
    const capabilities = await checkPopupCapabilities();

    if (!capabilities.allowed) {
        showStatus('❌ Popup blocker detected! Please allow popups for this site.', 'error');

        // Offer alternative: copy URLs to clipboard
        if (navigator.clipboard) {
            try {
                await navigator.clipboard.writeText(urls.join('\n'));
                showStatus('📋 URLs copied to clipboard as fallback!', 'success');
            } catch (err) {
                console.error('Failed to copy to clipboard:', err);
            }
        }
        return;
    }

    // Use advanced opening for better success rate
    openTabsAdvanced(urls, {
        delay: 150,
        batchSize: 3,
        showProgress: true
    });
}

/**
 * Handle different browser-specific behaviors
 */
function getBrowserInfo() {
    const userAgent = navigator.userAgent;

    if (userAgent.includes('Firefox')) {
        return {
            name: 'Firefox',
            maxConcurrentTabs: 3,
            recommendedDelay: 200
        };
    } else if (userAgent.includes('Chrome')) {
        return {
            name: 'Chrome',
            maxConcurrentTabs: 5,
            recommendedDelay: 100
        };
    } else if (userAgent.includes('Safari')) {
        return {
            name: 'Safari',
            maxConcurrentTabs: 2,
            recommendedDelay: 300
        };
    } else {
        return {
            name: 'Unknown',
            maxConcurrentTabs: 3,
            recommendedDelay: 150
        };
    }
}

/**
 * Optimize tab opening based on browser
 * @param {Array} urls - URLs to open
 */
function optimizedTabOpening(urls) {
    const browser = getBrowserInfo();

    console.log(`Detected browser: ${browser.name}`);

    openTabsAdvanced(urls, {
        delay: browser.recommendedDelay,
        batchSize: browser.maxConcurrentTabs,
        showProgress: true
    });
}
