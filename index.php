<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';

// Get all saved lists for initial page load
$savedLists = getAllLists();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Multi-Tab Website Opener Pro</title>
    <link rel="stylesheet" href="assets/styles.css">
</head>
<body>
    <div class="container">
        <h1>🚀 Multi-Tab Opener Pro</h1>
        
        <div class="info">
            <strong>How to use:</strong> Enter URLs or domain names (one per line). Protocols (https://) will be auto-added to domain names. Save lists and open all tabs with one click!
        </div>
        
        <div class="warning">
            <strong>Note:</strong> Make sure to allow popups for this page in Firefox settings.
        </div>
        
        <div class="main-content">
            <div class="url-input-section">
                <h3>📝 URL Input</h3>
                <textarea id="urlList" placeholder="https://www.google.com
https://www.github.com
https://www.stackoverflow.com
https://www.mozilla.org
https://www.wikipedia.org"></textarea>
                
                <div class="button-group">
                    <button class="open-btn" onclick="openAllTabs()">
                        🚀 Open All Tabs
                    </button>
                    <button class="clear-btn" onclick="clearUrls()">
                        🗑️ Clear
                    </button>
                </div>
                
                <div class="save-new-section">
                    <h4>💾 Save Current URLs</h4>
                    <input type="text" id="newListName" placeholder="Enter list name..." maxlength="50">
                    <button class="save-btn" onclick="saveCurrentList()">Save List</button>
                </div>
            </div>
            
            <div class="saved-lists-section">
                <h3>📚 Saved Lists</h3>
                <div id="savedListsContainer">
                    <?php if (empty($savedLists)): ?>
                        <p style="text-align: center; color: #666; font-style: italic;">No saved lists yet</p>
                    <?php else: ?>
                        <?php foreach ($savedLists as $list): ?>
                            <div class="saved-list-item">
                                <div class="saved-list-info">
                                    <div class="saved-list-name"><?= htmlspecialchars($list['name']) ?></div>
                                    <div class="saved-list-meta">
                                        <?= $list['count'] ?> URLs • <?= date('M j, Y', strtotime($list['created'])) ?>
                                    </div>
                                </div>
                                <div class="saved-list-actions">
                                    <button class="load-btn" onclick="loadSavedList('<?= htmlspecialchars($list['name']) ?>')">
                                        📁 Load
                                    </button>
                                    <button class="delete-btn" onclick="deleteSavedList('<?= htmlspecialchars($list['name']) ?>')">
                                        🗑️ Delete
                                    </button>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <div class="presets">
            <button class="preset-btn" onclick="loadPreset('social')">📱 Social Media</button>
            <button class="preset-btn" onclick="loadPreset('news')">📰 News Sites</button>
            <button class="preset-btn" onclick="loadPreset('dev')">💻 Developer Tools</button>
            <button class="preset-btn" onclick="loadPreset('productivity')">📊 Productivity</button>
        </div>
        
        <div id="status" class="status" style="display: none;"></div>
    </div>

    <script src="assets/presets.js"></script>
    <script src="assets/utils.js"></script>
    <script src="assets/tab-manager.js"></script>
    <script src="assets/list-manager.js"></script>
    <script src="assets/ui-manager.js"></script>
    <script src="assets/main.js"></script>
</body>
</html>
