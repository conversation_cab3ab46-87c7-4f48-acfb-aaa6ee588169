<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';

// Only handle POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

// Set JSON response header
header('Content-Type: application/json');

// Get and decode input
$input = json_decode(file_get_contents('php://input'), true);
$action = $input['action'] ?? '';

// Route to appropriate handler
try {
    switch ($action) {
        case 'save_list':
            $result = handleSaveList($input);
            break;

        case 'load_list':
            $result = handleLoadList($input);
            break;

        case 'delete_list':
            $result = handleDeleteList($input);
            break;

        case 'get_all_lists':
            $result = handleGetAllLists();
            break;

        default:
            $result = ['success' => false, 'message' => 'Invalid action'];
    }
} catch (Exception $e) {
    error_log("API Error: " . $e->getMessage());
    $result = ['success' => false, 'message' => 'Server error occurred'];
}

echo json_encode($result);
exit;

function handleSaveList($input) {
    $listName = trim($input['name'] ?? '');
    $urls = $input['urls'] ?? [];

    if (empty($listName) || empty($urls)) {
        return ['success' => false, 'message' => 'List name and URLs are required'];
    }

    if (strlen($listName) > MAX_LIST_NAME_LENGTH) {
        return ['success' => false, 'message' => 'List name too long'];
    }

    if (count($urls) > MAX_URLS_PER_LIST) {
        return ['success' => false, 'message' => 'Too many URLs'];
    }

    if (saveList($listName, $urls)) {
        return ['success' => true, 'message' => 'List saved successfully'];
    } else {
        return ['success' => false, 'message' => 'Failed to save list'];
    }
}

function handleLoadList($input) {
    $listName = trim($input['name'] ?? '');
    if (empty($listName)) {
        return ['success' => false, 'message' => 'List name required'];
    }

    $data = loadList($listName);
    if ($data !== false) {
        return ['success' => true, 'data' => $data];
    } else {
        return ['success' => false, 'message' => 'List not found'];
    }
}

function handleDeleteList($input) {
    $listName = trim($input['name'] ?? '');
    if (empty($listName)) {
        return ['success' => false, 'message' => 'List name required'];
    }

    if (deleteList($listName)) {
        return ['success' => true, 'message' => 'List deleted successfully'];
    } else {
        return ['success' => false, 'message' => 'Failed to delete list'];
    }
}

function handleGetAllLists() {
    $lists = getAllLists();
    return ['success' => true, 'lists' => $lists];
}
?>
