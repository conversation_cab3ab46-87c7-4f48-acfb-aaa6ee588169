# Multi-Tab Opener Pro - File Structure

## 📁 Directory Layout
```
multi-tab-opener/
├── index.php                 # Main entry point
├── api.php                   # AJAX request handler
├── includes/
│   ├── config.php            # Application configuration
│   └── functions.php         # Core PHP functions
├── assets/
│   ├── styles.css            # CSS styles
│   ├── main.js               # Main application logic
│   ├── utils.js              # Utility functions
│   ├── presets.js            # Preset URL collections
│   ├── tab-manager.js        # Tab opening functions
│   ├── list-manager.js       # List management functions
│   └── ui-manager.js         # UI management functions
└── url_lists/                # Auto-created directory for saved lists
    ├── my_dev_sites.json
    ├── daily_news.json
    └── social_media.json
```

## 📋 File Descriptions

### **Backend Files**
- **index.php** - Main HTML page with PHP integration
- **api.php** - Handles all AJAX requests (save, load, delete lists)
- **includes/config.php** - Constants and configuration settings
- **includes/functions.php** - Core PHP functions for file operations

### **Frontend Files**
- **assets/styles.css** - All CSS styles and responsive design
- **assets/main.js** - Application initialization and event handling
- **assets/utils.js** - Utility functions (AJAX, validation, parsing)
- **assets/presets.js** - Predefined URL collections
- **assets/tab-manager.js** - Tab opening and popup management
- **assets/list-manager.js** - Save/load/delete list operations
- **assets/ui-manager.js** - UI enhancements and user interactions

### **Data Storage**
- **url_lists/** - Directory containing JSON files for each saved list

## 🚀 Setup Instructions

1. **Upload all files** to your web server
2. **Ensure PHP support** is available
3. **Set write permissions** on the main directory (for url_lists folder creation)
4. **Access via browser**: `http://yourserver/multi-tab-opener/`

## 🔧 Key Features

### **Modular Architecture**
- ✅ Separated concerns (PHP backend, JS frontend)
- ✅ Each function group in its own file
- ✅ Easy to maintain and extend
- ✅ Clear file organization

### **Backend (PHP)**
- ✅ RESTful API design
- ✅ File-based storage (no database needed)
- ✅ Input validation and sanitization
- ✅ Error handling and logging

### **Frontend (JavaScript)**
- ✅ Modern async/await syntax
- ✅ Modular function organization
- ✅ Event-driven architecture
- ✅ Responsive UI management

## 🎯 Benefits of This Structure

1. **Maintainability** - Each file has a single responsibility
2. **Scalability** - Easy to add new features
3. **Debugging** - Easier to locate and fix issues
4. **Team Development** - Multiple developers can work on different files
5. **Performance** - Smaller, focused files load faster
6. **Security** - Separated API layer with proper validation

## 🔄 Data Flow

1. **User Action** → UI Manager → List Manager
2. **List Manager** → Utils (AJAX) → API
3. **API** → Functions → File System
4. **Response** ← Functions ← API ← Utils ← List Manager
5. **UI Update** ← List Manager ← UI Manager

This clean separation makes the codebase much more professional and maintainable!
